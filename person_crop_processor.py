import cv2
import numpy as np
import os
import math

def parse_yolo_detection(line):
    """
    解析YOLO检测结果的一行
    格式: class_id center_x center_y width height
    返回: (class_id, center_x, center_y, width, height)
    """
    parts = line.strip().split()
    if len(parts) != 5:
        return None
    
    class_id = int(parts[0])
    center_x = float(parts[1])
    center_y = float(parts[2])
    width = float(parts[3])
    height = float(parts[4])
    
    return class_id, center_x, center_y, width, height

def yolo_to_bbox(center_x, center_y, width, height, img_width, img_height):
    """
    将YOLO格式的归一化坐标转换为边界框坐标
    """
    # 转换为像素坐标
    center_x_px = center_x * img_width
    center_y_px = center_y * img_height
    width_px = width * img_width
    height_px = height * img_height
    
    # 计算边界框的左上角和右下角坐标
    x1 = int(center_x_px - width_px / 2)
    y1 = int(center_y_px - height_px / 2)
    x2 = int(center_x_px + width_px / 2)
    y2 = int(center_y_px + height_px / 2)
    
    return x1, y1, x2, y2

def calculate_iou(box1, box2):
    """
    计算两个边界框的IoU
    box格式: (x1, y1, x2, y2)
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # 计算交集区域
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集区域
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0

def is_face_inside_person(person_box, face_box):
    """
    检查人脸框是否在人框内部
    """
    x1_p, y1_p, x2_p, y2_p = person_box
    x1_f, y1_f, x2_f, y2_f = face_box

    # 人脸框的中心点
    face_center_x = (x1_f + x2_f) / 2
    face_center_y = (y1_f + y2_f) / 2

    # 检查人脸中心是否在人框内
    return (x1_p <= face_center_x <= x2_p and y1_p <= face_center_y <= y2_p)

def find_matching_face(person_box, face_boxes, iou_threshold=0.05):
    """
    为人框找到匹配的人脸框
    优先选择在人框内部的人脸框，然后考虑IoU
    """
    best_face = None
    best_score = 0

    for face_box in face_boxes:
        # 首先检查人脸是否在人框内部
        if is_face_inside_person(person_box, face_box):
            # 如果在内部，计算IoU作为评分
            iou = calculate_iou(person_box, face_box)
            score = iou + 1.0  # 给内部的人脸框额外加分

            if score > best_score:
                best_score = score
                best_face = face_box
        else:
            # 如果不在内部，只考虑IoU
            iou = calculate_iou(person_box, face_box)
            if iou > iou_threshold and iou > best_score:
                best_score = iou
                best_face = face_box

    return best_face

def update_person_box_ymax(person_box, face_box):
    """
    更新人框的ymax：取人框ymax和(人框ymin + 脸框高度*2.7)的较小值
    """
    x1_p, y1_p, x2_p, y2_p = person_box
    x1_f, y1_f, x2_f, y2_f = face_box
    
    face_height = y2_f - y1_f
    new_ymax = min(y2_p, y1_p + face_height * 2.7)
    
    return (x1_p, y1_p, x2_p, int(new_ymax))

def scale_box_by_center(box, scale_factor, img_width, img_height):
    """
    以中心点不变的方式缩放边界框
    """
    x1, y1, x2, y2 = box
    
    # 计算中心点和当前尺寸
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2
    width = x2 - x1
    height = y2 - y1
    
    # 计算新的尺寸
    new_width = width * scale_factor
    new_height = height * scale_factor
    
    # 计算新的边界框
    new_x1 = int(center_x - new_width / 2)
    new_y1 = int(center_y - new_height / 2)
    new_x2 = int(center_x + new_width / 2)
    new_y2 = int(center_y + new_height / 2)
    
    # 确保不超出图像范围
    new_x1 = max(0, new_x1)
    new_y1 = max(0, new_y1)
    new_x2 = min(img_width - 1, new_x2)
    new_y2 = min(img_height - 1, new_y2)
    
    return (new_x1, new_y1, new_x2, new_y2)

def expand_to_16_9_ratio(box, img_width, img_height):
    """
    将边界框扩展为16:9的比例，保证不超出图像范围
    """
    x1, y1, x2, y2 = box
    
    # 计算当前尺寸和中心点
    current_width = x2 - x1
    current_height = y2 - y1
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2
    
    # 目标比例 16:9
    target_ratio = 16.0 / 9.0
    current_ratio = current_width / current_height
    
    if current_ratio > target_ratio:
        # 当前太宽，需要增加高度
        new_width = current_width
        new_height = current_width / target_ratio
    else:
        # 当前太高，需要增加宽度
        new_height = current_height
        new_width = current_height * target_ratio
    
    # 计算新的边界框
    new_x1 = int(center_x - new_width / 2)
    new_y1 = int(center_y - new_height / 2)
    new_x2 = int(center_x + new_width / 2)
    new_y2 = int(center_y + new_height / 2)
    
    # 确保不超出图像范围，如果超出则调整
    if new_x1 < 0:
        offset = -new_x1
        new_x1 = 0
        new_x2 = min(img_width - 1, new_x2 + offset)
    
    if new_x2 >= img_width:
        offset = new_x2 - (img_width - 1)
        new_x2 = img_width - 1
        new_x1 = max(0, new_x1 - offset)
    
    if new_y1 < 0:
        offset = -new_y1
        new_y1 = 0
        new_y2 = min(img_height - 1, new_y2 + offset)
    
    if new_y2 >= img_height:
        offset = new_y2 - (img_height - 1)
        new_y2 = img_height - 1
        new_y1 = max(0, new_y1 - offset)
    
    return (new_x1, new_y1, new_x2, new_y2)

def process_person_crops(image_path, txt_path, output_dir):
    """
    处理单张图片的人物裁剪
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像文件 {image_path}")
        return False
    
    img_height, img_width = image.shape[:2]
    print(f"图像尺寸: {img_width} x {img_height}")
    
    # 读取检测结果
    if not os.path.exists(txt_path):
        print(f"错误: 检测结果文件不存在 {txt_path}")
        return False
    
    # 解析所有检测框
    person_boxes = []
    face_boxes = []
    
    with open(txt_path, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            detection = parse_yolo_detection(line)
            if detection is None:
                print(f"警告: 第{line_num}行格式错误: {line}")
                continue
            
            class_id, center_x, center_y, width, height = detection
            
            # 转换为边界框坐标
            x1, y1, x2, y2 = yolo_to_bbox(center_x, center_y, width, height, img_width, img_height)
            
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))
            
            if class_id == 0:  # 人
                person_boxes.append((x1, y1, x2, y2))
            elif class_id == 1:  # 人脸
                face_boxes.append((x1, y1, x2, y2))
    
    print(f"检测到 {len(person_boxes)} 个人框，{len(face_boxes)} 个人脸框")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取图像文件名（不含扩展名）
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    
    # 处理每个人框
    for i, person_box in enumerate(person_boxes):
        print(f"\n处理第 {i+1} 个人框: {person_box}")
        
        # 找到匹配的人脸框
        matching_face = find_matching_face(person_box, face_boxes)
        
        if matching_face is None:
            print(f"  警告: 未找到匹配的人脸框，跳过")
            continue
        
        print(f"  找到匹配的人脸框: {matching_face}")
        
        # 更新人框的ymax
        updated_person_box = update_person_box_ymax(person_box, matching_face)
        print(f"  更新ymax后的人框: {updated_person_box}")
        
        # 放大1.2倍
        scaled_box = scale_box_by_center(updated_person_box, 1.2, img_width, img_height)
        print(f"  放大1.2倍后的框: {scaled_box}")
        
        # 扩展为16:9比例
        final_box = expand_to_16_9_ratio(scaled_box, img_width, img_height)
        print(f"  扩展为16:9后的框: {final_box}")
        
        # 裁剪图像
        x1, y1, x2, y2 = final_box
        cropped_image = image[y1:y2+1, x1:x2+1]
        
        if cropped_image.size == 0:
            print(f"  错误: 裁剪区域为空")
            continue
        
        # 缩放为1920x1080
        resized_image = cv2.resize(cropped_image, (1920, 1080))
        
        # 保存结果
        output_filename = f"{image_name}_person_{i+1}.jpg"
        output_path = os.path.join(output_dir, output_filename)
        
        success = cv2.imwrite(output_path, resized_image)
        if success:
            print(f"  ✓ 保存成功: {output_path}")
        else:
            print(f"  ✗ 保存失败: {output_path}")
    
    return True

def main():
    # 指定的图片和标注文件
    image_path = r"D:\Download\20250911-2\images\125.jpg"
    txt_path = r"D:\Download\20250911-2\images\125.txt"
    output_dir = r"D:\Download\20250911-2\person_crops"
    
    print(f"处理图片: {image_path}")
    print(f"标注文件: {txt_path}")
    print(f"输出目录: {output_dir}")
    
    success = process_person_crops(image_path, txt_path, output_dir)
    
    if success:
        print("\n处理完成!")
    else:
        print("\n处理失败!")

if __name__ == "__main__":
    main()
